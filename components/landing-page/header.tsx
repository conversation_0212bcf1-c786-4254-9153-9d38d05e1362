'use client';

import { Menu } from 'lucide-react';
import { useState } from 'react';
import { useUser, SignInButton, UserButton } from '@clerk/nextjs';
import Link from 'next/link';

export default function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const { isSignedIn, user } = useUser();

    return (
        <header className="fixed w-full bg-white/80 backdrop-blur-sm z-50 border-b border-gray-200">
            <div className="container mx-auto px-6 py-4 flex justify-between items-center">
                {/* Logo */}
                <a
                    href="#"
                    className="flex items-center space-x-2 text-2xl font-bold text-gray-900"
                >
                    <svg
                        width="32"
                        height="32"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="text-indigo-600"
                    >
                        <path
                            d="M4 22V8.5C4 7.22876 4 6.59313 4.25301 6.0754C4.47467 5.62137 4.82137 5.27467 5.2754 5.05301C5.79313 4.8 6.42876 4.8 7.7 4.8H12M4 22L8 18M4 22L1 19"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                        <path
                            d="M20 2H10.5C9.22876 2 8.59313 2 8.0754 2.25301C7.62137 2.47467 7.27467 2.82137 7.05301 3.2754C6.8 3.79313 6.8 4.42876 6.8 5.7V15.5C6.8 16.7712 6.8 17.4069 7.05301 17.9246C7.27467 18.3786 7.62137 18.7253 8.0754 18.947C8.59313 19.2 9.22876 19.2 10.5 19.2H14.5M20 2L16 6M20 2L23 5"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>
                    <span>Unfurl</span>
                </a>
                {/* Desktop Navigation */}
                <nav className="hidden md:flex items-center space-x-8">
                    <a
                        href="#features"
                        className="text-gray-600 hover:text-indigo-600"
                    >
                        Features
                    </a>
                    <a
                        href="#use-cases"
                        className="text-gray-600 hover:text-indigo-600"
                    >
                        Use Cases
                    </a>
                    <a
                        href="#api"
                        className="text-gray-600 hover:text-indigo-600"
                    >
                        API
                    </a>
                    <a
                        href="#pricing"
                        className="text-gray-600 hover:text-indigo-600"
                    >
                        Pricing
                    </a>
                </nav>
                {/* Action Buttons */}
                <div className="hidden md:flex items-center space-x-4">
                    {isSignedIn ? (
                        <>
                            <Link
                                href="/dashboard"
                                className="text-gray-600 hover:text-indigo-600"
                            >
                                Dashboard
                            </Link>
                            <UserButton
                                appearance={{
                                    elements: {
                                        avatarBox: 'w-8 h-8',
                                    },
                                }}
                            />
                        </>
                    ) : (
                        <>
                            <SignInButton mode="modal">
                                <button className="text-gray-600 hover:text-indigo-600">
                                    Log In
                                </button>
                            </SignInButton>
                            <Link
                                href="/sign-up"
                                className="bg-indigo-600 text-white px-5 py-2 rounded-lg shadow hover:bg-indigo-700 transition"
                            >
                                Get Started Free
                            </Link>
                        </>
                    )}
                </div>
                {/* Mobile Menu Button */}
                <button
                    id="mobile-menu-button"
                    className="md:hidden"
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                >
                    <Menu />
                </button>
            </div>
            {/* Mobile Menu */}
            <div
                id="mobile-menu"
                className={`${
                    isMenuOpen ? '' : 'hidden'
                } md:hidden px-6 pb-4 space-y-2`}
            >
                <a
                    href="#features"
                    className="block text-gray-600 hover:text-indigo-600"
                >
                    Features
                </a>
                <a
                    href="#use-cases"
                    className="block text-gray-600 hover:text-indigo-600"
                >
                    Use Cases
                </a>
                <a
                    href="#api"
                    className="block text-gray-600 hover:text-indigo-600"
                >
                    API
                </a>
                <a
                    href="#pricing"
                    className="block text-gray-600 hover:text-indigo-600"
                >
                    Pricing
                </a>
                <div className="border-t border-gray-200 pt-4 space-y-2">
                    {isSignedIn ? (
                        <>
                            <Link
                                href="/dashboard"
                                className="block text-gray-600 hover:text-indigo-600"
                            >
                                Dashboard
                            </Link>
                            <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600">
                                    {user?.firstName || 'User'}
                                </span>
                                <UserButton
                                    appearance={{
                                        elements: {
                                            avatarBox: 'w-6 h-6',
                                        },
                                    }}
                                />
                            </div>
                        </>
                    ) : (
                        <>
                            <SignInButton mode="modal">
                                <button className="block w-full text-left text-gray-600 hover:text-indigo-600">
                                    Log In
                                </button>
                            </SignInButton>
                            <Link
                                href="/sign-up"
                                className="block bg-indigo-600 text-white text-center px-5 py-2 rounded-lg shadow hover:bg-indigo-700 transition"
                            >
                                Get Started Free
                            </Link>
                        </>
                    )}
                </div>
            </div>
        </header>
    );
}
