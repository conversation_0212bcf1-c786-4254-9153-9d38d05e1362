import { Twitter, Github, Linkedin } from 'lucide-react';

export default function Footer() {
    return (
        <footer className="bg-gray-900 text-white">
            <div className="container mx-auto px-6 py-12">
                <div className="grid md:grid-cols-4 gap-8">
                    {/* About */}
                    <div>
                        <h4 className="text-lg font-bold">Unfurl</h4>
                        <p className="mt-4 text-gray-400">
                            Intelligent data extraction, simplified.
                        </p>
                    </div>
                    {/* Links */}
                    <div>
                        <h5 className="font-semibold tracking-wider uppercase">
                            Product
                        </h5>
                        <nav className="mt-4 space-y-2">
                            <a
                                href="#features"
                                className="block text-gray-400 hover:text-white"
                            >
                                Features
                            </a>
                            <a
                                href="#pricing"
                                className="block text-gray-400 hover:text-white"
                            >
                                Pricing
                            </a>
                            <a
                                href="#api"
                                className="block text-gray-400 hover:text-white"
                            >
                                API Docs
                            </a>
                        </nav>
                    </div>
                    {/* Company */}
                    <div>
                        <h5 className="font-semibold tracking-wider uppercase">
                            Company
                        </h5>
                        <nav className="mt-4 space-y-2">
                            <a
                                href="#"
                                className="block text-gray-400 hover:text-white"
                            >
                                About Us
                            </a>
                            <a
                                href="#"
                                className="block text-gray-400 hover:text-white"
                            >
                                Contact
                            </a>
                            <a
                                href="#"
                                className="block text-gray-400 hover:text-white"
                            >
                                Careers
                            </a>
                        </nav>
                    </div>
                    {/* Legal */}
                    <div>
                        <h5 className="font-semibold tracking-wider uppercase">
                            Legal
                        </h5>
                        <nav className="mt-4 space-y-2">
                            <a
                                href="#"
                                className="block text-gray-400 hover:text-white"
                            >
                                Privacy Policy
                            </a>
                            <a
                                href="#"
                                className="block text-gray-400 hover:text-white"
                            >
                                Terms of Service
                            </a>
                        </nav>
                    </div>
                </div>
                <div className="mt-12 border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p className="text-gray-500">
                        &copy; 2025 Unfurl, Inc. All rights reserved.
                    </p>
                    <div className="flex space-x-4 mt-4 md:mt-0">
                        <a href="#" className="text-gray-500 hover:text-white">
                            <Twitter />
                        </a>
                        <a href="#" className="text-gray-500 hover:text-white">
                            <Github />
                        </a>
                        <a href="#" className="text-gray-500 hover:text-white">
                            <Linkedin />
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
}
