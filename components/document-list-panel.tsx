"use client"

import { UploadCloud } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

// Mock document data - in real app this would come from API
const mockDocuments = [
  {
    id: "1",
    name: "Invoice_INV-2025-06-11.pdf",
    status: "needs_review",
    date: "Today",
  },
  {
    id: "2", 
    name: "Receipt_TotalGas_0610.jpg",
    status: "completed",
    date: "Yesterday",
  },
  {
    id: "3",
    name: "Contract_ABC_Corp.pdf", 
    status: "processing",
    date: "2 days ago",
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "needs_review":
      return "bg-yellow-100 text-yellow-800"
    case "completed":
      return "bg-green-100 text-green-800"
    case "processing":
      return "bg-blue-100 text-blue-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "needs_review":
      return "Needs Review"
    case "completed":
      return "Completed"
    case "processing":
      return "Processing"
    default:
      return "Unknown"
  }
}

interface DocumentListPanelProps {
  selectedDocumentId?: string
  onDocumentSelect?: (documentId: string) => void
}

export function DocumentListPanel({ 
  selectedDocumentId, 
  onDocumentSelect 
}: DocumentListPanelProps) {
  return (
    <div className="w-full md:w-1/3 lg:w-1/4 xl:w-1/5 bg-white border-r border-gray-200 flex flex-col">
      {/* Upload Button */}
      <div className="p-4 border-b">
        <Button className="w-full bg-indigo-600 hover:bg-indigo-700">
          <UploadCloud className="w-5 h-5 mr-2" />
          Upload Document
        </Button>
      </div>

      {/* Document List */}
      <div className="flex-1 overflow-y-auto p-2 space-y-1">
        {mockDocuments.map((document) => (
          <div
            key={document.id}
            className={`
              block p-3 rounded-md cursor-pointer transition-colors
              ${
                selectedDocumentId === document.id
                  ? "bg-indigo-50 border-l-4 border-indigo-500"
                  : "hover:bg-gray-50"
              }
            `}
            onClick={() => onDocumentSelect?.(document.id)}
          >
            <p className="font-semibold text-sm text-gray-800 truncate">
              {document.name}
            </p>
            <div className="flex items-center justify-between text-xs mt-1">
              <Badge 
                variant="secondary" 
                className={getStatusColor(document.status)}
              >
                {getStatusText(document.status)}
              </Badge>
              <span className="text-gray-500">{document.date}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
