"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

// Mock extracted data - in real app this would come from API
const mockExtractedData = {
  invoice_number: "INV-12345",
  vendor_name: "Stark Industries", 
  invoice_date: "2025-06-12",
  total_amount: "$9,876.54",
  due_date: "2025-07-12",
}

const fieldLabels = {
  invoice_number: "Invoice Number",
  vendor_name: "Vendor Name",
  invoice_date: "Invoice Date", 
  total_amount: "Total Amount",
  due_date: "Due Date",
}

interface DataExtractionPanelProps {
  documentId?: string
  onFieldFocus?: (field: string) => void
  onFieldBlur?: () => void
}

export function DataExtractionPanel({ 
  documentId, 
  onFieldFocus, 
  onFieldBlur 
}: DataExtractionPanelProps) {
  const [extractedData, setExtractedData] = useState(mockExtractedData)

  const handleInputChange = (field: string, value: string) => {
    setExtractedData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleApprove = () => {
    // In real app, this would save the data to the backend
    console.log("Approving extracted data:", extractedData)
  }

  if (!documentId) {
    return (
      <div className="w-full md:w-1/3 lg:w-1/4 xl:w-1/4 bg-white border-l border-gray-200 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Extracted Data</h2>
        </div>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
              <svg
                className="w-8 h-8 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <p className="text-gray-500 text-sm">
              Select a document to view extracted data
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full md:w-1/3 lg:w-1/4 xl:w-1/4 bg-white border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-lg font-semibold">Extracted Data</h2>
      </div>

      {/* Form Fields */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {Object.entries(extractedData).map(([field, value]) => (
          <div key={field} className="form-field">
            <label className="text-sm font-medium text-gray-600 flex items-center">
              {fieldLabels[field as keyof typeof fieldLabels]}
            </label>
            <Input
              type={field.includes("date") ? "date" : "text"}
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value)}
              onFocus={() => onFieldFocus?.(field)}
              onBlur={() => onFieldBlur?.()}
              className="mt-1"
              data-field={field}
            />
          </div>
        ))}
      </div>

      {/* Footer with Approve Button */}
      <div className="p-4 border-t bg-white">
        <Button 
          onClick={handleApprove}
          className="w-full bg-green-600 hover:bg-green-700"
        >
          Approve & Finalize
        </Button>
      </div>
    </div>
  )
}
