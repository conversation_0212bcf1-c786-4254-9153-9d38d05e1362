'use client';

import { Button } from './ui/button';
import { Progress } from './ui/progress';

export function NavCreditsUsage() {
    // Mock credit data - in real app this would come from API/database
    const creditsUsed = 42;
    const creditsTotal = 50;
    const creditsPercentage = (creditsUsed / creditsTotal) * 100;

    return (
        <div>
            <p className="text-sm text-gray-600">
                Credits Used: {creditsUsed} / {creditsTotal}
            </p>
            <Progress value={creditsPercentage} className="mt-2" />
            <Button size="sm" className="w-full mt-3">
                Upgrade Plan
            </Button>
        </div>
    );
}
