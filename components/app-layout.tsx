'use client';

import { useState } from 'react';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/app-sidebar';
import { AppHeader } from '@/components/app-header';
import { DocumentListPanel } from '@/components/document-list-panel';
import { DocumentViewer } from '@/components/document-viewer';
import { DataExtractionPanel } from '@/components/data-extraction-panel';

interface AppLayoutProps {
    children?: React.ReactNode;
    title?: string;
    showDocumentWorkspace?: boolean;
}

export function AppLayout({
    children,
    title = 'Dashboard',
    showDocumentWorkspace = false,
}: AppLayoutProps) {
    const [selectedDocumentId, setSelectedDocumentId] = useState<string>();
    const [activeField, setActiveField] = useState<string>();

    const handleDocumentSelect = (documentId: string) => {
        setSelectedDocumentId(documentId);
    };

    const handleFieldFocus = (field: string) => {
        setActiveField(field);
    };

    const handleFieldBlur = () => {
        setActiveField(undefined);
    };

    return (
        <SidebarProvider
            style={
                {
                    '--sidebar-width': 'calc(var(--spacing) * 72)',
                    '--header-height': 'calc(var(--spacing) * 12)',
                } as React.CSSProperties
            }
        >
            <AppSidebar variant="inset" />
            <SidebarInset>
                <AppHeader title={title} />

                {showDocumentWorkspace ? (
                    /* Three-panel document workspace layout */
                    <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
                        <DocumentListPanel
                            selectedDocumentId={selectedDocumentId}
                            onDocumentSelect={handleDocumentSelect}
                        />
                        <DocumentViewer
                            documentId={selectedDocumentId}
                            activeField={activeField}
                        />
                        <DataExtractionPanel
                            documentId={selectedDocumentId}
                            onFieldFocus={handleFieldFocus}
                            onFieldBlur={handleFieldBlur}
                        />
                    </div>
                ) : (
                    /* Regular content layout */
                    <main className="flex-1 overflow-auto">{children}</main>
                )}
            </SidebarInset>
        </SidebarProvider>
    );
}
