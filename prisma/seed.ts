import { PrismaClient } from '../lib/generated/prisma'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create pre-built templates for common document types
  const invoiceTemplate = await prisma.template.upsert({
    where: { id: 'invoice-template' },
    update: {},
    create: {
      id: 'invoice-template',
      name: 'Invoice Template',
      description: 'Standard invoice data extraction template',
      documentType: 'invoice',
      isPublic: true,
      fields: {
        invoice_number: { type: 'string', required: true },
        date: { type: 'date', required: true },
        due_date: { type: 'date', required: false },
        vendor_name: { type: 'string', required: true },
        vendor_address: { type: 'string', required: false },
        total_amount: { type: 'number', required: true },
        tax_amount: { type: 'number', required: false },
        line_items: { 
          type: 'array', 
          required: false,
          items: {
            description: { type: 'string' },
            quantity: { type: 'number' },
            unit_price: { type: 'number' },
            total: { type: 'number' }
          }
        }
      }
    }
  })

  const receiptTemplate = await prisma.template.upsert({
    where: { id: 'receipt-template' },
    update: {},
    create: {
      id: 'receipt-template',
      name: 'Receipt Template',
      description: 'Standard receipt data extraction template',
      documentType: 'receipt',
      isPublic: true,
      fields: {
        merchant_name: { type: 'string', required: true },
        date: { type: 'date', required: true },
        time: { type: 'time', required: false },
        total_amount: { type: 'number', required: true },
        tax_amount: { type: 'number', required: false },
        payment_method: { type: 'string', required: false },
        items: {
          type: 'array',
          required: false,
          items: {
            name: { type: 'string' },
            price: { type: 'number' },
            quantity: { type: 'number' }
          }
        }
      }
    }
  })

  const businessCardTemplate = await prisma.template.upsert({
    where: { id: 'business-card-template' },
    update: {},
    create: {
      id: 'business-card-template',
      name: 'Business Card Template',
      description: 'Business card contact information extraction template',
      documentType: 'business_card',
      isPublic: true,
      fields: {
        name: { type: 'string', required: true },
        title: { type: 'string', required: false },
        company: { type: 'string', required: false },
        email: { type: 'email', required: false },
        phone: { type: 'phone', required: false },
        address: { type: 'string', required: false },
        website: { type: 'url', required: false }
      }
    }
  })

  console.log('✅ Created pre-built templates:', {
    invoice: invoiceTemplate.id,
    receipt: receiptTemplate.id,
    businessCard: businessCardTemplate.id
  })

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
