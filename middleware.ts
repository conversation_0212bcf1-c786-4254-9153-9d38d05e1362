import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define protected routes that require authentication
const isProtectedRoute = createRouteMatcher([
    '/dashboard(.*)',
    '/api/documents(.*)',
    '/api/jobs(.*)',
    '/api/templates(.*)',
    '/api/user(.*)',
    '/api/credits(.*)',
    '/settings(.*)',
    '/profile(.*)',
]);

// Define public API routes that use API key authentication instead of Clerk
const isPublicApiRoute = createRouteMatcher(['/api/public(.*)']);

export default clerkMiddleware(async (auth, req) => {
    // Allow public API routes to pass through (they handle their own auth)
    if (isPublicApiRoute(req)) {
        return;
    }

    // Protect all other routes that require authentication
    if (isProtectedRoute(req)) {
        await auth.protect();
    }
});

export const config = {
    matcher: [
        // Skip Next.js internals and all static files, unless found in search params
        '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
        // Always run for API routes
        '/(api|trpc)(.*)',
    ],
};
