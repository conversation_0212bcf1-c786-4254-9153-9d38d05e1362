/**
 * Example usage of the database operations
 * This file demonstrates how to use the Prisma client and helper functions
 * in your Next.js application.
 */

import { prisma } from '@/lib/prisma';
import {
    userOperations,
    documentOperations,
    jobOperations,
    templateOperations,
    apiKeyOperations,
    extractedDataOperations,
    creditOperations,
} from '@/lib/db';

// Example 1: User Management with Credits
export async function createUserExample() {
    // Create a new user (using Clerk user ID)
    const user = await userOperations.create({
        id: 'clerk_user_123', // Clerk user ID
        email: '<EMAIL>',
        name: '<PERSON>',
        imageUrl: 'https://example.com/avatar.jpg',
    });

    // Find user by email
    const foundUser = await userOperations.findByEmail('<EMAIL>');

    // Check credit balance
    const creditBalance = await userOperations.checkCreditBalance(user.id);
    console.log('Credit balance:', creditBalance);

    // Update user subscription (automatically updates credit limits)
    await userOperations.updateSubscription(
        user.id,
        'PROFESSIONAL', // 2000 credits/month
        'stripe_customer_id',
        'stripe_subscription_id'
    );

    return user;
}

// Example 2: Credit-Based Document Processing Workflow
export async function documentProcessingExample(userId: string) {
    // 1. Calculate required credits (1 credit per page - assume 3-page document)
    const documentPages = 3; // This would be determined by document analysis
    const requiredCredits = documentPages; // 1 credit = 1 page
    const hasCredits = await userOperations.hasCredits(userId, requiredCredits);

    if (!hasCredits) {
        throw new Error(
            `Insufficient credits for document processing. Need ${requiredCredits} credits for ${documentPages} pages.`
        );
    }

    // 2. Create a document record
    const document = await documentOperations.create({
        filename: 'invoice_001.pdf',
        originalName: 'Invoice from Acme Corp.pdf',
        mimeType: 'application/pdf',
        size: 1024000,
        storageUrl: 'https://s3.amazonaws.com/bucket/invoice_001.pdf',
        userId,
    });

    // 3. Create a processing job
    const job = await jobOperations.create({
        userId,
        documentId: document.id,
        type: 'EXTRACT',
        templateId: 'invoice-template', // Use pre-built template
    });

    // 4. Consume credits for processing (1 credit per page)
    await creditOperations.consumeCredits(
        userId,
        requiredCredits,
        `Document processing: invoice_001.pdf (${documentPages} pages)`,
        job.id,
        document.id
    );

    // 5. Update job status as it progresses
    await jobOperations.updateStatus(job.id, 'RUNNING');
    await jobOperations.updateProgress(job.id, 50);

    // 6. Store extracted data
    const extractedData = await extractedDataOperations.create({
        data: {
            invoice_number: 'INV-001',
            date: '2024-01-15',
            vendor_name: 'Acme Corp',
            total_amount: 1250.0,
        },
        confidence: 0.95,
        userId,
        documentId: document.id,
        jobId: job.id,
    });

    // 7. Complete the job
    await jobOperations.updateStatus(job.id, 'COMPLETED');

    // 8. Update document status
    await documentOperations.updateStatus(document.id, 'COMPLETED');

    return { document, job, extractedData };
}

// Example 3: Template Management
export async function templateExample(userId: string) {
    // Get public templates
    const publicTemplates = await templateOperations.findPublic();

    // Create a custom template
    const customTemplate = await templateOperations.create({
        name: 'Custom Invoice Template',
        description: 'Template for our specific invoice format',
        documentType: 'invoice',
        fields: {
            po_number: { type: 'string', required: true },
            invoice_number: { type: 'string', required: true },
            date: { type: 'date', required: true },
            vendor_name: { type: 'string', required: true },
            total_amount: { type: 'number', required: true },
        },
        userId,
    });

    // Get user's templates
    const userTemplates = await templateOperations.findByUser(userId);

    return { publicTemplates, customTemplate, userTemplates };
}

// Example 4: API Key Management
export async function apiKeyExample(userId: string) {
    // Generate a new API key
    const apiKey = await apiKeyOperations.create({
        name: 'Production API Key',
        key: 'unfurl_' + Math.random().toString(36).substring(2, 15),
        userId,
    });

    // Authenticate with API key
    const authenticatedKey = await apiKeyOperations.findByKey(apiKey.key);

    if (authenticatedKey && authenticatedKey.isActive) {
        // Update usage statistics
        await apiKeyOperations.updateUsage(authenticatedKey.id);
    }

    return apiKey;
}

// Example 5: Data Validation Workflow
export async function dataValidationExample(
    extractedDataId: string,
    userId: string
) {
    // Validate extracted data
    const validatedData = await extractedDataOperations.validate(
        extractedDataId,
        userId
    );

    return validatedData;
}

// Example 6: Complex Query with Relations
export async function complexQueryExample(userId: string) {
    // Get user with all related data
    const userWithData = await prisma.user.findUnique({
        where: { id: userId },
        include: {
            documents: {
                include: {
                    jobs: {
                        include: {
                            extractedData: true,
                        },
                    },
                },
                orderBy: { uploadedAt: 'desc' },
                take: 10,
            },
            templates: true,
            apiKeys: {
                where: { isActive: true },
            },
        },
    });

    return userWithData;
}

// Example 7: Usage Statistics
export async function getUsageStats(userId: string) {
    const stats = await prisma.user.findUnique({
        where: { id: userId },
        select: {
            monthlyDocumentCount: true,
            subscriptionPlan: true,
            _count: {
                select: {
                    documents: true,
                    jobs: true,
                    templates: true,
                    apiKeys: true,
                },
            },
        },
    });

    return stats;
}

// Example 8: Batch Operations
export async function batchOperationsExample() {
    // Use transactions for batch operations
    const result = await prisma.$transaction(async (tx) => {
        // Create multiple templates at once
        const templates = await tx.template.createMany({
            data: [
                {
                    name: 'Receipt Template v2',
                    documentType: 'receipt',
                    isPublic: true,
                    fields: {
                        /* template config */
                    },
                },
                {
                    name: 'Form Template',
                    documentType: 'form',
                    isPublic: true,
                    fields: {
                        /* template config */
                    },
                },
            ],
        });

        // Update multiple jobs
        const updatedJobs = await tx.job.updateMany({
            where: { status: 'PENDING' },
            data: { status: 'CANCELLED' },
        });

        return { templates, updatedJobs };
    });

    return result;
}

// Example 9: Credit Management
export async function creditManagementExample(userId: string) {
    // Add credits via purchase
    await creditOperations.addCredits(
        userId,
        1000,
        'PURCHASE',
        'Credit purchase via Stripe'
    );

    // Add bonus credits
    await creditOperations.addCredits(
        userId,
        100,
        'BONUS',
        'Welcome bonus credits'
    );

    // Get credit history
    const creditHistory = await creditOperations.getCreditHistory(userId, 20);

    // Reset monthly credits (typically done via cron job)
    await creditOperations.resetMonthlyCredits(userId);

    // Check current balance
    const balance = await userOperations.checkCreditBalance(userId);

    return { creditHistory, balance };
}

// Example 10: Credit Usage Analytics
export async function getCreditAnalytics(userId: string) {
    const analytics = await prisma.user.findUnique({
        where: { id: userId },
        select: {
            creditBalance: true,
            totalCreditsUsed: true,
            monthlyCreditsUsed: true,
            monthlyCreditsLimit: true,
            subscriptionPlan: true,
            creditTransactions: {
                where: {
                    createdAt: {
                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
                    },
                },
                select: {
                    amount: true,
                    type: true,
                    createdAt: true,
                },
                orderBy: { createdAt: 'desc' },
            },
        },
    });

    // Calculate usage statistics
    const totalConsumed =
        analytics?.creditTransactions
            .filter((t) => t.type === 'CONSUMPTION')
            .reduce((sum, t) => sum + Math.abs(t.amount), 0) || 0;

    const totalAdded =
        analytics?.creditTransactions
            .filter((t) =>
                ['PURCHASE', 'SUBSCRIPTION', 'BONUS'].includes(t.type)
            )
            .reduce((sum, t) => sum + t.amount, 0) || 0;

    return {
        ...analytics,
        last30DaysConsumed: totalConsumed,
        last30DaysAdded: totalAdded,
        usagePercentage: analytics
            ? (analytics.monthlyCreditsUsed / analytics.monthlyCreditsLimit) *
              100
            : 0,
    };
}

// Example 11: Flat Credit Calculation
export async function flatCreditCalculationExample() {
    // Simple examples of credit calculation
    const examples = [
        { document: '1-page invoice', pages: 1, credits: 1 },
        { document: '5-page contract', pages: 5, credits: 5 },
        { document: '10-page report', pages: 10, credits: 10 },
        { document: 'Single image receipt', pages: 1, credits: 1 },
    ];

    console.log('Credit Calculation Examples (1 credit = 1 page):');
    examples.forEach((example) => {
        const calculatedCredits = documentOperations.calculateCreditsRequired(
            example.pages
        );
        console.log(
            `${example.document}: ${example.pages} pages = ${calculatedCredits} credits`
        );
    });

    // Example: Check if user can process documents
    const userId = 'clerk_user_123';
    const userBalance = await userOperations.checkCreditBalance(userId);

    if (userBalance) {
        console.log(`User has ${userBalance.creditBalance} credits available`);

        examples.forEach((example) => {
            const canProcess = userBalance.creditBalance >= example.credits;
            console.log(
                `Can process ${example.document}: ${canProcess ? 'Yes' : 'No'}`
            );
        });
    }

    return examples;
}
