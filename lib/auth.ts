import { auth, currentUser } from '@clerk/nextjs/server';
import { userOperations } from './db';

/**
 * Get the current authenticated user from Clerk
 * Returns null if not authenticated
 */
export async function getCurrentUser() {
  try {
    const user = await currentUser();
    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Get the current user's ID from Clerk
 * Returns null if not authenticated
 */
export async function getCurrentUserId() {
  try {
    const { userId } = auth();
    return userId;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
}

/**
 * Get the current user from the database
 * Creates the user if they don't exist
 */
export async function getCurrentDbUser() {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    let dbUser = await userOperations.findById(user.id);
    
    // If user doesn't exist in database, create them
    if (!dbUser) {
      dbUser = await userOperations.create({
        id: user.id,
        email: user.emailAddresses[0]?.emailAddress || '',
        name: user.fullName || user.firstName || '',
        imageUrl: user.imageUrl,
      });
    }

    return dbUser;
  } catch (error) {
    console.error('Error getting current database user:', error);
    return null;
  }
}

/**
 * Require authentication - throws error if not authenticated
 * Use this in API routes that require authentication
 */
export async function requireAuth() {
  const { userId } = auth();
  if (!userId) {
    throw new Error('Unauthorized');
  }
  return userId;
}

/**
 * Require authentication and return database user
 * Creates user if they don't exist
 */
export async function requireDbUser() {
  const userId = await requireAuth();
  const user = await getCurrentUser();
  
  if (!user) {
    throw new Error('User not found');
  }

  let dbUser = await userOperations.findById(userId);
  
  // If user doesn't exist in database, create them
  if (!dbUser) {
    dbUser = await userOperations.create({
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress || '',
      name: user.fullName || user.firstName || '',
      imageUrl: user.imageUrl,
    });
  }

  return dbUser;
}
