import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { AppLayout } from '@/components/app-layout';

export default async function TemplatesPage() {
  const user = await currentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  return (
    <AppLayout title="Templates">
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Templates Coming Soon
            </h2>
            <p className="text-gray-600">
              Create and manage data extraction templates for your documents.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
