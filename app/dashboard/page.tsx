import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { userOperations } from '@/lib/db';
import { AppLayout } from '@/components/app-layout';
import { Button } from '@/components/ui/button';

export default async function DashboardPage() {
    const user = await currentUser();

    if (!user) {
        redirect('/sign-in');
    }

    // Get user data from database
    let dbUser;
    try {
        dbUser = await userOperations.findById(user.id);

        // If user doesn't exist in database, create them
        if (!dbUser) {
            dbUser = await userOperations.create({
                id: user.id,
                email: user.emailAddresses[0]?.emailAddress || '',
                name: user.fullName || user.firstName || '',
                imageUrl: user.imageUrl,
            });
        }
    } catch (error) {
        console.error('Error fetching/creating user:', error);
        // For now, show a basic dashboard even if DB operations fail
    }

    return (
        <AppLayout title="Dashboard">
            <div className="p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">
                            Welcome back, {user.firstName || 'User'}!
                        </h1>
                        <p className="text-lg text-gray-600">
                            Your AI-powered data extraction workspace
                        </p>
                    </div>

                    {dbUser && (
                        <div className="bg-white rounded-lg shadow p-6 mb-8">
                            <h2 className="text-xl font-semibold mb-4">
                                Account Information
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div>
                                    <p className="text-sm text-gray-600">
                                        Email
                                    </p>
                                    <p className="font-medium">
                                        {dbUser.email}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">
                                        Credits Available
                                    </p>
                                    <p className="font-medium text-green-600">
                                        {dbUser.creditBalance}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">
                                        Subscription Plan
                                    </p>
                                    <p className="font-medium">
                                        {dbUser.subscriptionPlan}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">
                                        Member Since
                                    </p>
                                    <p className="font-medium">
                                        {new Date(
                                            dbUser.createdAt
                                        ).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-white rounded-lg shadow p-6">
                            <h3 className="text-lg font-semibold mb-2">
                                Upload Documents
                            </h3>
                            <p className="text-gray-600 mb-4">
                                Start extracting data from your documents
                            </p>
                            <Button asChild className="w-full">
                                <Link href="/documents">Go to Documents</Link>
                            </Button>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <h3 className="text-lg font-semibold mb-2">
                                View Templates
                            </h3>
                            <p className="text-gray-600 mb-4">
                                Manage your data extraction templates
                            </p>
                            <Button variant="outline" className="w-full">
                                Coming Soon
                            </Button>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <h3 className="text-lg font-semibold mb-2">
                                API Access
                            </h3>
                            <p className="text-gray-600 mb-4">
                                Generate API keys for integration
                            </p>
                            <Button variant="outline" className="w-full">
                                Coming Soon
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
