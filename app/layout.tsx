import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'Unfurl - Intelligent Data Extraction, Simplified.',
    description:
        'Unfurl is the AI-powered platform that ends manual data entry. Extract structured data from invoices, receipts, and forms with incredible accuracy.',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <ClerkProvider>
            <html lang="en">
                <body className={inter.className}>{children}</body>
            </html>
        </ClerkProvider>
    );
}
