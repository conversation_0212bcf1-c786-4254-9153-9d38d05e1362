# **Project Task List: Unfurl (Next.js Stack)**

This document outlines the major tasks required to build and launch the Unfurl application using a full-stack Next.js architecture. The project is broken down into four sprints, each representing a major development phase.

### **Sprint 1: Core Application Setup & Authentication**

**Goal:** Get a basic, secure application running with user accounts and database connectivity.

* Task ID: S1-01
  Task: Initialize Next.js Project
  Description: Set up a new Next.js (App Router) project with TypeScript and Tailwind CSS. Establish the basic project structure (app, components, lib, etc.).
* Task ID: S1-02
  Task: Set Up Database with Prisma
  Description: Define the database schema using schema.prisma for User, Job, Template, and other necessary models with credit-based usage tracking. Set up Prisma Client for type-safe database access.
* Task ID: S1-03
  Task: Implement Authentication
  Description: Integrate Clerk to handle user authentication. Implement login, registration, and session management for both email/password and Google social login with credit-based user management.
* Task ID: S1-04
  Task: Create Core UI Layout
  Description: Build the main application shell, including the responsive sidebar, top user navigation bar, and user profile/settings page. Connect this to the authentication state.

### **Sprint 2: Document Processing & Validation Workflow**

**Goal:** Implement the core functionality of uploading, processing, and validating documents.

* Task ID: S2-01
  Task: Implement File Uploads
  Description: Create the file upload mechanism. This includes the interactive upload modal on the frontend and a Next.js API route on the backend to handle the file and store it securely on a cloud storage service (e.g., AWS S3).
* Task ID: S2-02
  Task: Set Up Asynchronous Job Queue
  Description: Integrate a serverless-friendly task queue (e.g., Vercel KV, Upstash) to handle background processing. When a file is uploaded, a job should be added to the queue.
* Task ID: S2-03
  Task: Integrate AI Service
  Description: Create a serverless function (triggered by the job queue) that calls a third-party AI service (e.g., AWS Textract, Google Vision API) to perform OCR and data extraction. The function will then update the job status and save the results to the database.
* Task ID: S2-04
  Task: Build the Document Validation UI
  Description: Implement the three-panel validation screen based on the interactive mockup. This includes fetching and displaying the document list, the document image, and the editable data form.

### **Sprint 3: Monetization & Public-Facing Pages**

**Goal:** Add payment processing and build out the marketing-facing parts of the site.

* Task ID: S3-01
  Task: Build Marketing & Pricing Pages
  Description: Convert the static HTML mockups for the landing page and pricing page into fully responsive pages within the Next.js application.
* Task ID: S3-02
  Task: Integrate Stripe for Subscriptions
  Description: Implement the credit-based pricing plans and integrate Stripe for handling subscriptions. This includes creating backend API routes for checkout sessions and webhooks to update user subscription status and credit allocations in the database.
* Task ID: S3-03
  Task: Implement Credit-Based Feature Gating
  Description: Lock or unlock application features based on the user's current credit balance and subscription plan (e.g., prevent document processing when credits are exhausted, show credit usage warnings).
* Task ID: S3-04
  Task: Build Developer API Endpoints
  Description: Implement the public-facing API routes (e.g., POST /api/documents) for developer access. Add API key generation and management to the user settings page.

### **Sprint 4: Pre-Launch Polish & Deployment**

**Goal:** Finalize the application, write tests, and prepare for a production launch.

* Task ID: S4-01
  Task: Write Comprehensive Tests
  Description: Set up Jest and React Testing Library. Write unit and integration tests for critical components, API routes, and utility functions to ensure application stability.
* Task ID: S4-02
  Task: Set Up CI/CD Pipeline
  Description: Create a continuous integration and deployment pipeline (e.g., using GitHub Actions) to automate testing and deployments to a staging and production environment on Vercel.
* Task ID: S4-03
  Task: Create User & API Documentation
  Description: Write comprehensive documentation for end-users and a detailed, public-facing documentation site for the developer API.
* Task ID: S4-04
  Task: Beta Testing & Feedback Implementation
  Description: Conduct a closed beta test with a select group of users. Collect feedback to identify bugs and make final usability improvements before the public launch.